package com.ym.synapse

import android.util.Log
import android.net.Uri
import androidx.activity.compose.LocalActivityResultRegistryOwner
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.platform.LocalContext
import com.ym.synapse.OcrScreen // Explicit import for clarity
import com.ym.synapse.OcrHandler // Explicit import for clarity // 请确保包名正确

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Security
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.tooling.preview.Preview
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.*
import com.ym.synapse.ui.theme.SynapseTheme // 假设您的主题是这个
import com.ym.synapse.utils.PermissionHelper
import com.google.accompanist.permissions.*

// 1. 定义导航路由
sealed class Screen(val route: String, val label: String, val icon: ImageVector) {
    object Home : Screen("home", "主页", Icons.Filled.Home)
    object Ocr : Screen("ocr", "OCR", Icons.Filled.Search)
    object Settings : Screen("settings", "设置", Icons.Filled.Settings)
}

val items = listOf(
    Screen.Home,
    Screen.Ocr,
    Screen.Settings
)

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            SynapseTheme { // 使用您项目的主题
                MainScreen()
            }
        }
    }
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun MainScreen() {
    val context = LocalContext.current
    val navController = rememberNavController()

    // 权限检测
    val permissionsToRequest = PermissionHelper.getPermissionRequestArray(context).toList()
    val multiplePermissionsState = rememberMultiplePermissionsState(permissionsToRequest)

    // 启动时检查权限
    LaunchedEffect(Unit) {
        if (!PermissionHelper.areAllRequiredPermissionsGranted(context)) {
            multiplePermissionsState.launchMultiplePermissionRequest()
        }
    }

    // 如果权限未授予，显示权限请求界面
    if (!multiplePermissionsState.allPermissionsGranted) {
        PermissionRequestScreen(
            multiplePermissionsState = multiplePermissionsState
        )
    } else {
        // 权限已授予，显示正常界面
        Scaffold(
        bottomBar = {
            NavigationBar {
                val navBackStackEntry by navController.currentBackStackEntryAsState()
                val currentDestination = navBackStackEntry?.destination
                items.forEach { screen ->
                    NavigationBarItem(
                        icon = { Icon(screen.icon, contentDescription = screen.label) },
                        label = { Text(screen.label) },
                        selected = currentDestination?.hierarchy?.any { it.route == screen.route } == true,
                        onClick = {
                            navController.navigate(screen.route) {
                                // Pop up to the start destination of the graph to
                                // avoid building up a large stack of destinations
                                // on the back stack as users select items
                                popUpTo(navController.graph.findStartDestination().id) {
                                    saveState = true
                                }
                                // Avoid multiple copies of the same destination when
                                // reselecting the same item
                                launchSingleTop = true
                                // Restore state when reselecting a previously selected item
                                restoreState = true
                            }
                        }
                    )
                }
            }
        }
    ) { innerPadding ->
        NavHost(navController, startDestination = Screen.Home.route, Modifier.padding(innerPadding)) {
            composable(Screen.Home.route) { HomeScreen() }
            composable(Screen.Ocr.route) {
                val context = LocalContext.current
                val activityResultRegistryOwner = LocalActivityResultRegistryOwner.current ?: return@composable // Ensure it's not null
                val coroutineScope = rememberCoroutineScope()

                val ocrHandler = remember(context, activityResultRegistryOwner, coroutineScope) {
                    OcrHandler(
                        context = context,
                        activityResultRegistryOwner = activityResultRegistryOwner,
                        onResult = { result ->
                            Log.d("MainActivityOcr", "OCR Result: $result")
                            // Handle result if needed, e.g., update a ViewModel
                        },
                        coroutineScope = coroutineScope
                    )
                }

                val imagePickerLauncher = rememberLauncherForActivityResult(
                    contract = ActivityResultContracts.GetContent(),
                    onResult = { uri: Uri? ->
                        ocrHandler.processUri(uri)
                    }
                )

                // Pass a lambda to OcrScreen that OcrScreen can call to launch the image picker
                // This requires OcrScreen to be modified to accept such a lambda.
                // For now, we assume OcrScreen's "Local OCR" button's onClick will be:
                // onClick = { 
                //    ocrHandler.selectImageForLocalOcr() // This calls prepareForImageSelection
                //    imagePickerLauncher.launch("image/*") 
                // }
                // We will adjust OcrScreen.kt next to make this cleaner.
                // For this step, let's just call OcrScreen and assume its internal button will trigger the launcher.
                // The OcrScreen will need access to the imagePickerLauncher or a way to signal MainActivity to launch it.
                // A cleaner way is to pass a launcher lambda to OcrScreen.

                // Let's define a function that OcrScreen can call
                val launchImagePickerAction = { 
                    ocrHandler.prepareForImageSelection() // Prepares the state in OcrHandler
                    imagePickerLauncher.launch("image/*")
                }

                // We'll need to modify OcrScreen to accept this action.
                // For now, let's assume OcrScreen is called and we'll fix the interaction in the next step.
                // OcrScreen(ocrHandler = ocrHandler) // Original call
                // We will need to modify OcrScreen to accept 'launchImagePickerAction'
                // For now, to make this step compilable, we'll call the original OcrScreen.
                // The interaction logic for the button will be refined in OcrScreen.kt or by adding a parameter to OcrScreen.

                // Placeholder for the actual OcrScreen call that will be refined.
                // For now, let's assume we will modify OcrScreen to take the launcher function.
                // This is a temporary structure to make this step valid.
                // The actual OcrScreen composable will be modified in the next step.
                OcrScreen(ocrHandler = ocrHandler, onLaunchImagePicker = launchImagePickerAction)
            }
            composable(Screen.Settings.route) { SettingsScreen() }
        }
    }
    }
}

// --- 权限请求屏幕 ---
@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun PermissionRequestScreen(
    multiplePermissionsState: MultiplePermissionsState
) {
    val context = LocalContext.current
    var permissions by remember { mutableStateOf(PermissionHelper.getAllPermissions(context)) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Filled.Security,
            contentDescription = null,
            modifier = Modifier.size(80.dp),
            tint = MaterialTheme.colorScheme.primary
        )

        Spacer(modifier = Modifier.height(24.dp))

        Text(
            text = "需要权限授权",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = "为了正常使用应用功能，需要您授予以下权限：",
            style = MaterialTheme.typography.bodyLarge,
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(24.dp))

        // 权限列表
        permissions.filter { !it.isGranted }.forEach { permission ->
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Warning,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.error,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Column {
                        Text(
                            text = permission.name,
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = permission.description,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        // 标准权限请求按钮
        val standardPermissions = permissions.filter {
            !it.isGranted && it.permission != "BATTERY_OPTIMIZATION" && it.permission != "AUTO_START"
        }

        if (standardPermissions.isNotEmpty()) {
            Button(
                onClick = {
                    multiplePermissionsState.launchMultiplePermissionRequest()
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("授予基础权限")
            }

            Spacer(modifier = Modifier.height(8.dp))
        }

        // 电池优化权限按钮
        val batteryPermission = permissions.find { it.permission == "BATTERY_OPTIMIZATION" && !it.isGranted }
        if (batteryPermission != null) {
            OutlinedButton(
                onClick = {
                    val intent = PermissionHelper.openBatteryOptimizationSettings(context)
                    intent?.let { context.startActivity(it) }
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("设置电池优化白名单")
            }

            Spacer(modifier = Modifier.height(8.dp))
        }

        // 自启动权限按钮
        val autoStartPermission = permissions.find { it.permission == "AUTO_START" && !it.isGranted }
        if (autoStartPermission != null) {
            OutlinedButton(
                onClick = {
                    val intent = PermissionHelper.openAutoStartSettings(context)
                    intent?.let { context.startActivity(it) }
                },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("设置自启动权限")
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 刷新状态按钮
        TextButton(
            onClick = {
                permissions = PermissionHelper.getAllPermissions(context)
            },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("刷新权限状态")
        }
    }
}

// --- 占位符屏幕 ---
@Composable
fun HomeScreen() {
    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        Text("主页屏幕 (占位符)")
    }
}

@Composable
fun OcrScreenPlaceholder() {
    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        Text("OCR 屏幕 (占位符 - 稍后集成)")
    }
}

@Composable
fun SettingsScreenPlaceholder() {
    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        Text("设置屏幕 (占位符 - 稍后创建)")
    }
}
// --- 结束占位符屏幕 ---

@Preview(showBackground = true)
@Composable
fun DefaultPreview() {
    SynapseTheme {
        MainScreen()
    }
}