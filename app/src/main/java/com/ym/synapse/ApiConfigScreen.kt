package com.ym.synapse

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.ym.synapse.api.ApiService
import com.ym.synapse.ui.theme.SynapseTheme
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ApiConfigScreen(
    onBackClick: () -> Unit = {},
    onConfigComplete: () -> Unit = {}
) {
    val context = LocalContext.current
    val sharedPreferences = remember {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
    
    // API配置状态
    var apiUrl by remember {
        mutableStateOf(sharedPreferences.getString(KEY_API_URL, "") ?: "")
    }
    var apiKey by remember {
        mutableStateOf(sharedPreferences.getString(KEY_API_KEY, "") ?: "")
    }
    var modelId by remember {
        mutableStateOf(sharedPreferences.getString(KEY_AI_OCR_MODEL_ID, DEFAULT_AI_OCR_MODEL_ID) ?: DEFAULT_AI_OCR_MODEL_ID)
    }

    var showApiKey by remember { mutableStateOf(false) }
    var showSuccessMessage by remember { mutableStateOf(false) }
    var isLoadingModels by remember { mutableStateOf(false) }
    var availableModels by remember { mutableStateOf<List<ApiService.ModelInfo>>(emptyList()) }
    var showModelDropdown by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf<String?>(null) }

    val apiService = remember { ApiService() }
    val coroutineScope = rememberCoroutineScope()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .statusBarsPadding()
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
    ) {
        // 顶部栏
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBackClick) {
                Icon(Icons.Default.ArrowBack, contentDescription = "返回")
            }
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "API配置",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 配置说明
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Check,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "配置OpenAI格式API",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "请配置兼容OpenAI格式的API服务，用于AI OCR功能",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // API URL配置
        OutlinedTextField(
            value = apiUrl,
            onValueChange = { apiUrl = it },
            label = { Text("API Base URL") },
            placeholder = { Text("https://api.openai.com/v1") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true,
            supportingText = {
                Text(
                    text = "只需输入基础URL，程序会自动补充 /chat/completions",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // API Key配置
        OutlinedTextField(
            value = apiKey,
            onValueChange = { apiKey = it },
            label = { Text("API Key") },
            placeholder = { Text("your-api-key") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true,
            visualTransformation = if (showApiKey) VisualTransformation.None else PasswordVisualTransformation(),
            trailingIcon = {
                IconButton(onClick = { showApiKey = !showApiKey }) {
                    Icon(
                        if (showApiKey) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                        contentDescription = if (showApiKey) "隐藏" else "显示"
                    )
                }
            },
            supportingText = {
                Text(
                    text = "输入您的API密钥，无需添加sk-前缀",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 模型ID配置
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.Bottom
        ) {
            OutlinedTextField(
                value = modelId,
                onValueChange = { modelId = it },
                label = { Text("模型ID") },
                placeholder = { Text("gpt-4o-mini") },
                modifier = Modifier.weight(1f),
                singleLine = true,
                trailingIcon = if (availableModels.isNotEmpty()) {
                    {
                        IconButton(onClick = { showModelDropdown = true }) {
                            Icon(Icons.Default.ArrowDropDown, contentDescription = "选择模型")
                        }
                    }
                } else null
            )

            Spacer(modifier = Modifier.width(8.dp))

            // 获取模型列表按钮
            OutlinedButton(
                onClick = {
                    if (apiUrl.isNotBlank() && apiKey.isNotBlank()) {
                        isLoadingModels = true
                        errorMessage = null
                        coroutineScope.launch {
                            when (val result = apiService.getModels(apiUrl, apiKey)) {
                                is ApiService.ApiResult.Success -> {
                                    availableModels = result.data
                                    isLoadingModels = false
                                    if (result.data.isNotEmpty()) {
                                        showModelDropdown = true
                                    }
                                }
                                is ApiService.ApiResult.Error -> {
                                    errorMessage = result.message
                                    isLoadingModels = false
                                }
                            }
                        }
                    } else {
                        errorMessage = "请先填写API URL和API Key"
                    }
                },
                enabled = !isLoadingModels && apiUrl.isNotBlank() && apiKey.isNotBlank()
            ) {
                if (isLoadingModels) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                } else {
                    Icon(Icons.Default.Refresh, contentDescription = "获取模型")
                }
            }
        }

        // 模型选择下拉菜单
        if (showModelDropdown && availableModels.isNotEmpty()) {
            DropdownMenu(
                expanded = showModelDropdown,
                onDismissRequest = { showModelDropdown = false }
            ) {
                availableModels.forEach { model ->
                    DropdownMenuItem(
                        text = {
                            Column {
                                Text(
                                    text = model.name,
                                    style = MaterialTheme.typography.bodyMedium
                                )
                                if (model.description.isNotEmpty()) {
                                    Text(
                                        text = model.description,
                                        style = MaterialTheme.typography.bodySmall,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                            }
                        },
                        onClick = {
                            modelId = model.id
                            showModelDropdown = false
                        }
                    )
                }
            }
        }

        // 错误消息
        if (errorMessage != null) {
            Spacer(modifier = Modifier.height(8.dp))
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                )
            ) {
                Text(
                    text = errorMessage!!,
                    modifier = Modifier.padding(12.dp),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // 保存按钮
        Button(
            onClick = {
                // 自动补充完整的API URL
                val fullApiUrl = if (apiUrl.endsWith("/chat/completions")) {
                    apiUrl
                } else if (apiUrl.endsWith("/")) {
                    "${apiUrl}chat/completions"
                } else {
                    "$apiUrl/chat/completions"
                }

                // 保存配置
                with(sharedPreferences.edit()) {
                    putString(KEY_API_URL, fullApiUrl)
                    putString(KEY_API_KEY, apiKey)
                    putString(KEY_AI_OCR_MODEL_ID, modelId)
                    apply()
                }
                showSuccessMessage = true
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = apiUrl.isNotBlank() && apiKey.isNotBlank() && modelId.isNotBlank()
        ) {
            Text("保存配置")
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 完成配置按钮
        if (apiUrl.isNotBlank() && apiKey.isNotBlank() && modelId.isNotBlank()) {
            Button(
                onClick = onConfigComplete,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.secondary
                )
            ) {
                Text("完成配置，进入应用")
            }
        }
        
        // 成功消息
        if (showSuccessMessage) {
            LaunchedEffect(Unit) {
                kotlinx.coroutines.delay(2000)
                showSuccessMessage = false
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.secondaryContainer
                )
            ) {
                Text(
                    text = "✓ 配置已保存",
                    modifier = Modifier.padding(16.dp),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSecondaryContainer
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ApiConfigScreenPreview() {
    SynapseTheme {
        ApiConfigScreen()
    }
}
