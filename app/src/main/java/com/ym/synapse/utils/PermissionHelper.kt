package com.ym.synapse.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.content.ContextCompat

/**
 * 权限管理工具类
 * 用于检测和管理应用所需的各种权限
 */
object PermissionHelper {

    /**
     * 权限信息数据类
     */
    data class PermissionInfo(
        val permission: String,
        val name: String,
        val description: String,
        val isRequired: Boolean,
        val isGranted: Boolean
    )

    /**
     * 检查通知权限
     */
    fun checkNotificationPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            // Android 13以下版本默认有通知权限
            true
        }
    }

    /**
     * 检查相册读取权限
     */
    fun checkMediaPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13及以上使用READ_MEDIA_IMAGES
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_MEDIA_IMAGES
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            // Android 12及以下使用READ_EXTERNAL_STORAGE
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * 检查文件读取权限
     */
    fun checkStoragePermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13及以上，检查READ_MEDIA_IMAGES权限
            checkMediaPermission(context)
        } else {
            // Android 12及以下，检查READ_EXTERNAL_STORAGE权限
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * 获取所有需要检测的权限列表
     */
    fun getAllPermissions(context: Context): List<PermissionInfo> {
        val permissions = mutableListOf<PermissionInfo>()

        // 通知权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permissions.add(
                PermissionInfo(
                    permission = Manifest.permission.POST_NOTIFICATIONS,
                    name = "通知权限",
                    description = "用于显示截图处理结果和状态通知",
                    isRequired = true,
                    isGranted = checkNotificationPermission(context)
                )
            )
        }

        // 相册读取权限
        val mediaPermission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Manifest.permission.READ_MEDIA_IMAGES
        } else {
            Manifest.permission.READ_EXTERNAL_STORAGE
        }

        permissions.add(
            PermissionInfo(
                permission = mediaPermission,
                name = "相册读取权限",
                description = "用于读取和处理截图文件",
                isRequired = true,
                isGranted = checkMediaPermission(context)
            )
        )

        return permissions
    }

    /**
     * 检查是否所有必需权限都已授予
     */
    fun areAllRequiredPermissionsGranted(context: Context): Boolean {
        return getAllPermissions(context).filter { it.isRequired }.all { it.isGranted }
    }

    /**
     * 获取未授予的必需权限列表
     */
    fun getMissingRequiredPermissions(context: Context): List<PermissionInfo> {
        return getAllPermissions(context).filter { it.isRequired && !it.isGranted }
    }

    /**
     * 获取权限请求数组（用于ActivityResultContracts.RequestMultiplePermissions）
     */
    fun getPermissionRequestArray(context: Context): Array<String> {
        return getAllPermissions(context)
            .filter { it.isRequired && !it.isGranted }
            .map { it.permission }
            .toTypedArray()
    }
}
