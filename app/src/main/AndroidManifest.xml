<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Permissions for Foreground Service and Notifications -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.INTERNET" />

    <!--
         Permissions for reading screenshots.
         - READ_EXTERNAL_STORAGE is for Android 12 (SDK 32) and below.
         - READ_MEDIA_IMAGES is for Android 13 (SDK 33) and above.
    -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <!-- 电池优化白名单权限 -->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Synapse"
        tools:targetApi="31">

        <!-- LSPosed Module Metadata -->
        <meta-data
            android:name="xposedmodule"
            android:value="true" />
        <meta-data
            android:name="xposeddescription"
            android:value="Intercepts double power press to trigger custom actions." />
        <meta-data
            android:name="xposedminversion"
            android:value="82" />
        <meta-data
            android:name="xposedscope"
            android:resource="@xml/xposed_scope" />

        <!-- Main Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.Synapse">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Broadcast Receiver for Boot Completed -->
        <receiver android:name=".hook.BootCompletedReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <!-- Broadcast Receiver for Hook -->
        <receiver
            android:name=".hook.PowerPressReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.ym.synapse.ACTION_DOUBLE_PRESS_POWER" />
            </intent-filter>
        </receiver>

        <!-- Service for Screenshot -->
        <service
            android:name=".hook.ScreenshotService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse">
            <property android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                android:value="Service to capture screenshots initiated by user hardware key press." />
        </service>

        <!-- Service to be started by Magisk at boot to keep the app process alive -->
        <!-- Service to be started by Magisk at boot to keep the app process alive -->
        <service
            android:name=".hook.KeepAliveService"
            android:enabled="true"
            android:exported="true" />

    </application>

</manifest>