<resources>
    <string name="app_name">synapse</string>
    <string name="ocr_title">OCR Recognition</string>
    <string name="ocr_button_local">Local OCR</string>
    <string name="ocr_button_ai">AI OCR</string>
    <string name="ocr_ai_coming_soon">AI OCR coming soon!</string>
    <string name="ocr_results_placeholder">Recognition results will appear here.</string>
    <string name="ocr_image_selected_processing">Image selected, processing...</string>
    <string name="ocr_load_image_failed">Failed to load image.</string>
    <string name="ocr_no_image_selected">No image selected.</string>
    <string name="ocr_select_image_prompt">Please select an image from your gallery.</string>
    <string name="ocr_no_text_found">No text found in image.</string>
    <string name="ocr_recognition_failed">Recognition failed: %s</string>
    <string name="ocr_image_preview_desc">Selected Image Preview</string>
    <string name="ocr_error_api_config_missing">AI OCR 失败：API 地址或 Key 未配置。请检查设置。</string>
    <string name="ocr_ai_processing">正在进行 AI OCR 识别...</string>
    <string name="settings_api_model_id_label">AI OCR Model ID</string>
    <string name="settings_api_url_hint">例如: https://your-api-server.com/v1/chat/completions</string>
    <string name="ocr_no_text_found_ai">AI OCR 未识别到文本。</string>
    <string name="ocr_error_ai_failed">AI OCR 识别失败：%1$s</string>
    <string name="ocr_error_network">网络请求失败：%1$s</string>
    <string name="ocr_prompt_select_image_for_ai">请先选择一张图片再进行 AI 识别</string>
</resources>